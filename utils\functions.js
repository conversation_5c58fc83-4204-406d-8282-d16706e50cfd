const jwt = require('jsonwebtoken');
const { permissions } = require("./permissions");
const { s3 } = require('../modules/awsS3');
const dayjs = require("dayjs");
const db = require("../modules/db");
const { forward } = require("mgrs");

const defaultDateTimeFormat = "MM/DD/YYYY h:mm:ss A";

// the coordinates must be sorted by timestamp in ascending order
const getSessionsByCoordinates = (coordinates) => {
    const sessions = [];

    let currentSession = [coordinates[0]];

    for (let i = 1; i < coordinates.length; i++) {
        if (new Date(coordinates[i].timestamp).getTime() - new Date(coordinates[i - 1].timestamp).getTime() < 300000) {
            currentSession.push(coordinates[i]);
        } else {
            if (currentSession.length > 1)
                sessions.push(currentSession);
            currentSession = [coordinates[i]];
        }
    }

    // Add the last session
    if (currentSession.length && currentSession.length > 1) {
        sessions.push(currentSession);
    }

    return sessions;
}

function getPreviousWeekDateTimeUTC() {
    const now = new Date();

    // Ensure we're working with UTC dates/times
    const currentUTC = Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds());

    // Get the current day of the week (0 = Sunday, 6 = Saturday)
    const currentDayOfWeek = now.getUTCDay();

    // Calculate the current Sunday at 16:00 UTC
    const thisSunday = new Date(currentUTC);
    thisSunday.setUTCDate(thisSunday.getUTCDate() - currentDayOfWeek);
    thisSunday.setUTCHours(16, 0, 0, 0); // Set to 16:00 UTC

    // If the current time is past this Sunday 16:00 UTC, return the previous Sunday to this Sunday
    if (currentUTC >= thisSunday.getTime()) {
        const previousSunday = new Date(thisSunday.getTime());
        previousSunday.setUTCDate(previousSunday.getUTCDate() - 7);

        return {
            start: previousSunday.toISOString(),
            end: new Date(thisSunday - 1000).toISOString(),
        };
    } else {
        // If the current time is before this Sunday 16:00 UTC, return two Sundays ago to the previous Sunday
        const previousSunday = new Date(thisSunday.getTime());
        previousSunday.setUTCDate(previousSunday.getUTCDate() - 7);

        const twoSundaysAgo = new Date(previousSunday.getTime());
        twoSundaysAgo.setUTCDate(twoSundaysAgo.getUTCDate() - 7);

        return {
            start: twoSundaysAgo.toISOString(),
            end: new Date(previousSunday - 1000).toISOString(),
        };
    }
}

function getPastDaylightDateTimeUTC() {
    const now = new Date();

    // Get the current UTC time and day of the year
    const currentUTCDate = new Date(now.getTime());

    // Calculate the daylight hours in GMT+8
    const getDaylightTimesGMT8 = (date) => {
        const year = date.getUTCFullYear();
        const month = date.getUTCMonth();
        const day = date.getUTCDate();

        // Start of daylight (05:00 GMT+8)
        const startOfDaylightGMT8 = new Date(Date.UTC(year, month, day, 5, 0, 0));
        startOfDaylightGMT8.setUTCHours(startOfDaylightGMT8.getUTCHours() - 8); // Convert GMT+8 to UTC

        // End of daylight (20:00 GMT+8)
        const endOfDaylightGMT8 = new Date(Date.UTC(year, month, day, 20, 0, 0));
        endOfDaylightGMT8.setUTCHours(endOfDaylightGMT8.getUTCHours() - 8); // Convert GMT+8 to UTC

        return { start: startOfDaylightGMT8, end: endOfDaylightGMT8 };
    };

    // Get today's daylight period in GMT+8 (converted to UTC)
    const { start: todayStart, end: todayEnd } = getDaylightTimesGMT8(currentUTCDate);

    // If current time is before or during today's daylight, return yesterday's daylight period
    if (now < todayEnd) {
        const yesterday = new Date(currentUTCDate);
        yesterday.setUTCDate(yesterday.getUTCDate() - 1);
        const { start: yesterdayStart, end: yesterdayEnd } = getDaylightTimesGMT8(yesterday);
        return {
            start: yesterdayStart.toISOString(),
            end: yesterdayEnd.toISOString()
        };
    }

    // If current time is after today's daylight period, return today's daylight period
    return {
        start: todayStart.toISOString(),
        end: todayEnd.toISOString()
    };
}

function getPreviousMonthDateTimeUTC() {
    const now = new Date();
    const start = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() - 1, 1));
    const end = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 1) - 1);

    return {
        start: start.toISOString(),
        end: end.toISOString()
    };
}

function offsetDate(offsetMillis, date = Date.now()) {
    return new Date(date + offsetMillis);
}

function buildUrl(url, params, host = process.env.API_URL) {
    const baseUrl = new URL(host + url);
    baseUrl.search = new URLSearchParams(params).toString();
    return baseUrl.toString();
}

function generateUnsubscribeToken(email, notificationId) {
    const payload = { email, notificationId: notificationId.toString(), timestamp: Date.now() };
    const secretKey = process.env.JWT_SECRET;
    return jwt.sign(payload, secretKey, { expiresIn: "180d" });
}

function generateToken(data) {
    const payload = { ...data, timestamp: Date.now() };
    const secretKey = process.env.JWT_SECRET;
    return jwt.sign(payload, secretKey, { expiresIn: "180d" });
}

function dateToString(date = new Date(), userDateFormat = defaultDateTimeFormat, onlyDate = false) {
    if (onlyDate) {
        userDateFormat = userDateFormat.split(/[ T]/)[0];
    }

    return dayjs(date).format(userDateFormat || defaultDateTimeFormat);
    // return `${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}-${date.getFullYear()}`;
}


function arraysIntersection(a, b) {
    return a.filter(Set.prototype.has, new Set(b));
}

async function fetchArtifactImageBase64({ bucket_name, image_path, aws_region }) {
    try {
        if (!bucket_name || !image_path || !aws_region) {
            throw new Error('Missing required parameters');
        }

        const params = {
            Bucket: bucket_name,
            Key: image_path
        };

        s3.config.update({ region: aws_region });
        const imageData = await s3.getObject(params).promise();
        const base64Image = imageData.Body.toString('base64');

        return base64Image;
    } catch (err) {
        throw err;
    }
}

async function fetchArtifactImageMetadata({ bucket_name, metadata_path, aws_region }) {
    try {
        if (!bucket_name || !metadata_path || !aws_region) {
            throw new Error('Missing required parameters');
        }

        s3.config.update({ region: aws_region });
        const metadata = await s3.getObject({
            Bucket: bucket_name,
            Key: metadata_path
        }).promise().then((data) => JSON.parse(data.Body.toString("utf-8")))

        return metadata;
    } catch (err) {
        throw err;
    }
}

async function calculateDistanceInMeters(lat1, lon1, lat2, lon2) {
    const LatLonSpherical = (await import('geodesy/latlon-spherical.js')).default;

    const point1 = new LatLonSpherical(lat1, lon1);
    const point2 = new LatLonSpherical(lat2, lon2);
    return point1.distanceTo(point2);
}

function displayCoordinates(coordinates, showMGRS = false) {
    if (!coordinates) return null;
    const [lng, lat] = coordinates;
    if (!showMGRS) {
        return coordinates.map((e) => e?.toFixed(8)).join(", ");
    } else {
        return forward([lng, lat]);
    }
}

function findUnitByUnitHistory({ vessels, from, to, unitId }) {
    if (!Array.isArray(vessels) || vessels.length === 0) throw new Error('vessels must be a non-empty array');
    if (!from) throw new Error('from is required');
    if (!to) throw new Error('to is required');
    if (!unitId) throw new Error('unitId is required');
    const fromTs = typeof from === "number" ? from : new Date(from).getTime();
    const toTs = typeof to === "number" ? to : new Date(to).getTime();
    for (const vessel of vessels) {
        if (!Array.isArray(vessel.units_history)) continue;
        for (const history of vessel.units_history) {
            const historyFrom = typeof history.mount_timestamp === "number" ? history.mount_timestamp : new Date(history.mount_timestamp).getTime();
            const historyTo =
                history.unmount_timestamp === undefined || history.unmount_timestamp === null
                    ? null
                    : typeof history.unmount_timestamp === "number"
                        ? history.unmount_timestamp
                        : new Date(history.unmount_timestamp).getTime();
            if (history.unit_id === unitId && historyFrom <= toTs && (historyTo === null || historyTo >= fromTs)) {
                return { vessel, unitId: history.unit_id };
            }
        }
    }
    return null;
}

async function getDBCollectionNames(dbInstance) {
    if (!dbInstance || !dbInstance.db) throw new Error('Invalid dbInstance provided')
    const collectionNames = (await db.qmLocations.db.listCollections().toArray()).filter(c => !c.name.startsWith('system.')).map(c => c.name)
    return collectionNames
}

module.exports = {
    getSessionsByCoordinates,
    getPreviousWeekDateTimeUTC,
    getPastDaylightDateTimeUTC,
    getPreviousMonthDateTimeUTC,
    offsetDate,
    buildUrl,
    generateUnsubscribeToken,
    generateToken,
    dateToString,
    arraysIntersection,
    fetchArtifactImageBase64,
    fetchArtifactImageMetadata,
    calculateDistanceInMeters,
    defaultDateTimeFormat,
    displayCoordinates,
    findUnitByUnitHistory,
    getDBCollectionNames
};
