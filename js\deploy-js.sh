#!/bin/bash

# JavaScript Microservice Deployment Script for AWS Linux
# Run this from the js/ directory

set -e

echo "📱 Deploying JavaScript Microservice..."

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install --production

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "⚙️ Creating .env file..."
    cat > .env << 'EOF'
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017
DB_NAME=qm

# Flask API Configuration
FLASK_API_URL=http://localhost:6000

# Other configurations
NODE_ENV=production
EOF
    echo "⚠️  Please update .env file with your actual configuration"
fi

echo "✅ JavaScript microservice setup completed!"
echo ""
echo "🚀 To start the microservice:"
echo "  npm start              - Start main service"
echo "  npm run evaluator      - Run artifact evaluator"
echo ""
echo "🔧 Available commands:"
echo "  npm run dev           - Development mode with nodemon"
echo "  npm run evaluator     - Run artifact evaluation"
