# Python Evaluator API Makefile

.PHONY: install dev start clean test

# Install dependencies
install:
	pip install -r requirements.txt

# Install in development mode
dev:
	pip install -e .

# Start the Flask API server
start:
	python flask_evaluator_api.py

# Start with hot reload
dev-start:
	FLASK_ENV=development python flask_evaluator_api.py

# Clean Python cache files
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +

# Run tests (placeholder)
test:
	echo "No tests specified"

# Show help
help:
	@echo "Available commands:"
	@echo "  make install    - Install dependencies"
	@echo "  make dev        - Install in development mode"
	@echo "  make start      - Start the Flask API server"
	@echo "  make dev-start  - Start with development mode"
	@echo "  make clean      - Clean Python cache files"
	@echo "  make test       - Run tests"
