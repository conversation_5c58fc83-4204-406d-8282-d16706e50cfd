const db = require('../modules/db');
const io = require('../modules/io');
const { offsetDate } = require("../utils/functions");
const { s3 } = require('../modules/awsS3');
const { processNotificationAlerts } = require("./notificationAlertsProcessor");
const { buildThumbnailImage } = require("../modules/awsS3");
const { processSeaVisionRequests } = require("./processSeaVisionRequests");
const { postLogToSlack } = require('../modules/notifyLog');
const axios = require('axios');
const detectingNewArtifactsPeriodic = 300000;
const processingSettingsLatestTimestampName = 'latest_processed_artifact_timestamp'

async function getLatestTimestamp() {
    var trackedLatestTimestamp = await db.qmai.collection('processing_settings').findOne({ name: processingSettingsLatestTimestampName });

    if (!trackedLatestTimestamp) {
        // note: this is not the best way to do this, but it's the only way to get the latest artifact
        const latestArtifact = await db.qmai.collection('analysis_results').findOne({}, { sort: { timestamp: -1 } });
        console.log('[getLatestTimestamp] latestArtifact', latestArtifact)
        const res = await db.qmai.collection('processing_settings').insertOne({ name: processingSettingsLatestTimestampName, value: new Date(latestArtifact.timestamp).getTime() });
        trackedLatestTimestamp = await db.qmai.collection('processing_settings').findOne({ _id: res.insertedId });
    }

    if (!trackedLatestTimestamp?.value) {
        throw new Error('[getLatestTimestamp] No latest timestamp found');
    }

    console.log('[getLatestTimestamp] trackedLatestTimestamp', trackedLatestTimestamp)

    return trackedLatestTimestamp.value;
}

async function evaluateDuplicateIndex(newArtifact) {
    try {
        // Skip if no vessel ID
        if (!newArtifact.onboard_vessel_id) {
            console.log(`[evaluateDuplicateIndex] No vessel ID for artifact ${newArtifact._id}, setting duplicate_index=0`);
            return 0;
        }

        // Find the previous artifact from the same vessel
        const prevArtifact = await db.qmai.collection("analysis_results")
            .findOne(
                {
                    onboard_vessel_id: newArtifact.onboard_vessel_id,
                    timestamp: { $lt: new Date(newArtifact.timestamp) }
                },
                { sort: { timestamp: -1 } }
            );

        // If no previous artifact, set duplicate_index=0
        if (!prevArtifact) {
            console.log(`[evaluateDuplicateIndex] No previous artifact for vessel ${newArtifact.onboard_vessel_id}, setting duplicate_index=0`);
            return 0;
        }

        console.log(`[evaluateDuplicateIndex] Evaluating artifact ${newArtifact._id} against previous ${prevArtifact._id}`);

        // Call Python evaluation API
        const response = await axios.post(`${process.env.FLASK_API_URL}/evaluate/sequential`, {
            artifacts: [prevArtifact, newArtifact]
        });

        if (response.data && response.data.length > 0) {
            const duplicateIndex = response.data[0].duplicate_index;
            console.log(`[evaluateDuplicateIndex] Evaluated duplicate_index=${duplicateIndex} for artifact ${newArtifact._id}`);
            return duplicateIndex;
        } else {
            console.log(`[evaluateDuplicateIndex] No evaluation result, setting duplicate_index=0`);
            return 0;
        }

    } catch (error) {
        console.error(`[evaluateDuplicateIndex] Error evaluating artifact ${newArtifact._id}:`, error.message);
        return 0; // Default to 0 on error
    }
}

async function setLatestTimestamp(artifacts) {
    if (!artifacts) throw new Error('[setLatestTimestamp] artifacts are required');
    if (!artifacts.length) throw new Error('[setLatestTimestamp] artifacts cannot be empty');

    const latestArtifact = artifacts.reduce((latest, current) => {
        return new Date(current.timestamp) > new Date(latest.timestamp) ? current : latest;
    }, artifacts[0]);

    console.log('[setLatestTimestamp] latestArtifact.timestamp', latestArtifact.timestamp)

    await db.qmai.collection('processing_settings').updateOne({ name: processingSettingsLatestTimestampName }, { $set: { value: new Date(latestArtifact.timestamp).getTime() } });
}

async function detectNewArtifacts() {
    try {
        const latestTimestamp = await getLatestTimestamp();

        const artifacts = await db.qmai.collection('analysis_results').aggregate([
            // get the objects that have timestamp greater than latestTimestamp
            {
                $match: {
                    timestamp: { $gt: new Date(latestTimestamp) },
                    vessel_presence: true,
                    // super_category: {$ne: null},
                    location: { $ne: null }
                }
            },
        ]).toArray();

        /**
        //fetch some random artifacts where category is not null and sub_category is not null and flags is not null 10 artifacts
        const artifacts = await db.qmai.collection('analysis_results').aggregate([
            { $match: { $or: [{ category: { $ne: null } }, { sub_category: { $ne: null } }, { country_flag: { $ne: null } }] } },            { $sample: { size: 10 } }
        ]).toArray();

        const artifacts = await db.qmai.collection('analysis_results').aggregate([
        { $match: { country_flag: { $nin: [null] }, timestamp: { $gte: new Date('2025-02-10') } } },
        ]).toArray();
        console.log(`[updateStatistics] artifacts length: ${artifacts.length}`);
        */

        if (!artifacts.length) {
            console.info('[detectNewArtifacts] No new artifacts are found');
            return;
        }

        if (artifacts.length > 100) {
            postLogToSlack({
                severity: 'warning',
                message: `Large number of artifacts detected\nTotal length: ${artifacts.length}`,
                stack: new Error().stack
            });
        }

        console.log('[detectNewArtifacts] artifacts', artifacts)

        await preprocessArtifacts(artifacts);
        console.log('[detectNewArtifacts] artifact updated');

        artifacts.forEach((artifact) => {
            io.emit('artifactDetected', artifact);
        });

        try {
            await processNotificationAlerts({ artifacts });
        } catch (err) {
            console.error('FATAL: [detectNewArtifacts] error occurred while processing notification alerts', err);
        }

        try {
            await processSeaVisionRequests(artifacts);
        } catch (err) {
            console.error('FATAL: [detectNewArtifacts] error occurred while processing SeaVision requests', err);
        }

        await setLatestTimestamp(artifacts);
    } catch (err) {
        console.error('FATAL: [detectNewArtifacts] error occurred', err);
        postLogToSlack({
            severity: 'fatal',
            message: 'Error post-processing artifact detections',
            stack: err.stack
        });
    } finally {
        setTimeout(detectNewArtifacts, detectingNewArtifactsPeriodic);
    }
}

async function preprocessArtifacts(artifacts) {
    const updates = await Promise.all(artifacts.map(async (artifact) => {
        if (!artifact.image_path || !artifact.bucket_name) {
            return null
        }

        let res = undefined;

        const thumbnail_image_path = await buildThumbnailImage(artifact.bucket_name, artifact.aws_region, artifact.image_path, artifact.unit_id);
        if (thumbnail_image_path) {
            artifact.thumbnail_image_path = thumbnail_image_path;
            res = { thumbnail_image_path };
        }

        const videoPath = artifact.image_path.replace('/image/', '/video/').replace('.jpg', '.mp4');
        const params = {
            Bucket: artifact.bucket_name,
            Key: videoPath,
        };

        try {
            const response = await s3.headObject(params).promise();
            console.info(`[updateVideoUrlObjectsDetected] Video is found`);
            artifact.video_path = videoPath;

            (res ||= {}).video_path = videoPath;
        } catch (err) {
            if (err.code !== 'NotFound') {
                console.error(`[updateVideoUrlObjectsDetected] Error while checking video: ${err}`);
            }
        }

        return res ? {
            updateOne: {
                filter: { _id: artifact._id },
                update: { $set: res }
            }
        } : null;
    }));

    const validUpdates = updates.filter(update => update !== null);

    if (validUpdates.length > 0) {
        const result = await db.qmai.collection('analysis_results').bulkWrite(validUpdates);
        console.log(`[updateVideoUrlObjectsDetected] Updated documents: ${JSON.stringify(result)}`);
    }
}

detectNewArtifacts();