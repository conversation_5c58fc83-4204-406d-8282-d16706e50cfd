const mongoose = require('mongoose');
const db = require('../modules/db');
const io = require('../modules/io');

const alertSchema = new mongoose.Schema({
    artifact_id: { type: mongoose.Schema.Types.ObjectId, required: true },
    type: { type: String, required: true, enum: ['foreign_vessel_detection'] },
    timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});

alertSchema.post('save', (alert) => {
    io.emit('alerts/insert', alert.toObject());
})

const Alert = db.qm.model('Alert', alertSchema);

module.exports = Alert