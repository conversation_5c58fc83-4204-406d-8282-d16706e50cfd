#!/bin/bash

# AWS Linux Deployment Script for Evaluator Microservice
# Run this script on your AWS EC2 instance after git pull

set -e  # Exit on any error

echo "🚀 Starting deployment on AWS Linux..."

# Update system packages
echo "📦 Updating system packages..."
sudo yum update -y

# Install Node.js (if not already installed)
if ! command -v node &> /dev/null; then
    echo "📦 Installing Node.js..."
    curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
    sudo yum install -y nodejs
fi

# Install Python 3.9+ (if not already installed)
if ! command -v python3.9 &> /dev/null; then
    echo "🐍 Installing Python 3.9..."
    sudo yum install -y python39 python39-pip python39-devel
    # Create symlink for python3
    sudo ln -sf /usr/bin/python3.9 /usr/bin/python3
    sudo ln -sf /usr/bin/pip3.9 /usr/bin/pip3
fi

# Install PM2 globally (for process management)
if ! command -v pm2 &> /dev/null; then
    echo "⚙️ Installing PM2..."
    sudo npm install -g pm2
fi

# Setup JavaScript microservice
echo "📱 Setting up JavaScript microservice..."
cd js/
npm install
cd ..

# Setup Python API service
echo "🐍 Setting up Python API service..."
cd python/
pip3 install --user -r requirements.txt
cd ..

# Create PM2 ecosystem file
echo "⚙️ Creating PM2 configuration..."
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'evaluator-api',
      script: 'python/flask_evaluator_api.py',
      interpreter: 'python3',
      cwd: '/home/<USER>/quartermaster-web-microservices',
      env: {
        PYTHONDONTWRITEBYTECODE: '1',
        FLASK_ENV: 'production'
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      error_file: './logs/evaluator-api-error.log',
      out_file: './logs/evaluator-api-out.log',
      log_file: './logs/evaluator-api-combined.log',
      time: true
    },
    {
      name: 'js-microservice',
      script: 'js/index.js',
      cwd: '/home/<USER>/quartermaster-web-microservices',
      env: {
        NODE_ENV: 'production'
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      error_file: './logs/js-microservice-error.log',
      out_file: './logs/js-microservice-out.log',
      log_file: './logs/js-microservice-combined.log',
      time: true
    }
  ]
};
EOF

# Create logs directory
mkdir -p logs

# Start services with PM2
echo "🚀 Starting services with PM2..."
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
echo "⚠️  Run the command above to enable PM2 startup on boot"

echo "✅ Deployment completed!"
echo ""
echo "📋 Service Status:"
pm2 status

echo ""
echo "🔧 Useful Commands:"
echo "  pm2 status              - Check service status"
echo "  pm2 logs evaluator-api  - View Python API logs"
echo "  pm2 logs js-microservice - View JS microservice logs"
echo "  pm2 restart all         - Restart all services"
echo "  pm2 stop all           - Stop all services"
echo ""
echo "🌐 Services:"
echo "  Python API: http://localhost:6000"
echo "  JS Microservice: http://localhost:3000 (if applicable)"
