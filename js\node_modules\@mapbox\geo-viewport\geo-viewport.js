!function(f){"object"==typeof exports&&"undefined"!=typeof module?module.exports=f():"function"==typeof define&&define.amd?define([],f):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).geoViewport=f()}(function(){return function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var l="function"==typeof require&&require;if(!u&&l)return l(o,!0);if(i)return i(o,!0);l=new Error("Cannot find module '"+o+"'");throw l.code="MODULE_NOT_FOUND",l}l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n||e)},l,l.exports,e,t,n,r)}return n[o].exports}for(var i="function"==typeof require&&require,o=0;o<r.length;o++)s(r[o]);return s}({1:[function(require,module,exports){var SphericalMercator=require("@mapbox/sphericalmercator"),smCache={};function fetchMerc(tileSize){return smCache[tileSize=tileSize||256]||(smCache[tileSize]=new SphericalMercator({size:tileSize})),smCache[tileSize]}module.exports.viewport=function(height,ratios,minzoom,maxzoom,width,zoom){minzoom=void 0===minzoom?0:minzoom,maxzoom=void 0===maxzoom?20:maxzoom;var merc=fetchMerc(width),center=maxzoom,centerPixelX=merc.px([height[0],height[1]],center),centerPixelY=merc.px([height[2],height[3]],center),width=centerPixelY[0]-centerPixelX[0],height=centerPixelX[1]-centerPixelY[1],centerPixelX=centerPixelX[0]+width/2,centerPixelY=centerPixelY[1]+height/2,ratios=[width/ratios[0],height/ratios[1]],zoom=function(base,adjusted,allowFloat){adjusted=Math.min(base-Math.log(adjusted[0])/Math.log(2),base-Math.log(adjusted[1])/Math.log(2));return allowFloat?adjusted:Math.floor(adjusted)}(center,ratios,zoom),center=merc.ll([centerPixelX,centerPixelY],center),zoom=Math.max(minzoom,Math.min(maxzoom,zoom));return{center:center,zoom:zoom}},module.exports.bounds=function(tl,br,dimensions,px){void 0!==tl.lon&&(tl=[tl.lon,tl.lat]);var merc=fetchMerc(px),px=merc.px(tl,br),tl=merc.ll([px[0]-dimensions[0]/2,px[1]-dimensions[1]/2],br),br=merc.ll([px[0]+dimensions[0]/2,px[1]+dimensions[1]/2],br);return[tl[0],br[1],br[0],tl[1]]}},{"@mapbox/sphericalmercator":2}],2:[function(require,module,exports){var SphericalMercator=function(){var cache={},D2R=Math.PI/180,R2D=180/Math.PI,A=6378137,MAXEXTENT=20037508.342789244;function isFloat(n){return Number(n)===n&&n%1!=0}function SphericalMercator(options){if(options=options||{},this.size=options.size||256,!cache[this.size]){var size=this.size,c=cache[this.size]={};c.Bc=[],c.Cc=[],c.zc=[],c.Ac=[];for(var d=0;d<30;d++)c.Bc.push(size/360),c.Cc.push(size/(2*Math.PI)),c.zc.push(size/2),c.Ac.push(size),size*=2}this.Bc=cache[this.size].Bc,this.Cc=cache[this.size].Cc,this.zc=cache[this.size].zc,this.Ac=cache[this.size].Ac}return SphericalMercator.prototype.px=function(ll,zoom){if(isFloat(zoom)){var f=this.size*Math.pow(2,zoom),d=f/2,x=f/360,y=f/(2*Math.PI),ac=f,f=Math.min(Math.max(Math.sin(D2R*ll[1]),-.9999),.9999);return ac<(x=d+ll[0]*x)&&(x=ac),ac<(y=d+.5*Math.log((1+f)/(1-f))*-y)&&(y=ac),[x,y]}d=this.zc[zoom],f=Math.min(Math.max(Math.sin(D2R*ll[1]),-.9999),.9999),x=Math.round(d+ll[0]*this.Bc[zoom]),y=Math.round(d+.5*Math.log((1+f)/(1-f))*-this.Cc[zoom]);return x>this.Ac[zoom]&&(x=this.Ac[zoom]),y>this.Ac[zoom]&&(y=this.Ac[zoom]),[x,y]},SphericalMercator.prototype.ll=function(px,zoom){if(isFloat(zoom)){var zc=this.size*Math.pow(2,zoom),bc=zc/360,g=zc/(2*Math.PI),zc=zc/2,g=(px[1]-zc)/-g;return[(px[0]-zc)/bc,R2D*(2*Math.atan(Math.exp(g))-.5*Math.PI)]}g=(px[1]-this.zc[zoom])/-this.Cc[zoom];return[(px[0]-this.zc[zoom])/this.Bc[zoom],R2D*(2*Math.atan(Math.exp(g))-.5*Math.PI)]},SphericalMercator.prototype.bbox=function(x,ur,bbox,ll,srs){ll&&(ur=Math.pow(2,bbox)-1-ur);ll=[x*this.size,(+ur+1)*this.size],ur=[(+x+1)*this.size,ur*this.size],bbox=this.ll(ll,bbox).concat(this.ll(ur,bbox));return"900913"===srs?this.convert(bbox,"900913"):bbox},SphericalMercator.prototype.xyz=function(bounds,tms,tms_style,x){"900913"===x&&(bounds=this.convert(bounds,"WGS84"));var px_ur=[bounds[0],bounds[1]],x=[bounds[2],bounds[3]],bounds=this.px(px_ur,tms),px_ur=this.px(x,tms),x=[Math.floor(bounds[0]/this.size),Math.floor((px_ur[0]-1)/this.size)],bounds=[Math.floor(px_ur[1]/this.size),Math.floor((bounds[1]-1)/this.size)],bounds={minX:Math.min.apply(Math,x)<0?0:Math.min.apply(Math,x),minY:Math.min.apply(Math,bounds)<0?0:Math.min.apply(Math,bounds),maxX:Math.max.apply(Math,x),maxY:Math.max.apply(Math,bounds)};return tms_style&&(tms={minY:Math.pow(2,tms)-1-bounds.maxY,maxY:Math.pow(2,tms)-1-bounds.minY},bounds.minY=tms.minY,bounds.maxY=tms.maxY),bounds},SphericalMercator.prototype.convert=function(bbox,to){return"900913"===to?this.forward(bbox.slice(0,2)).concat(this.forward(bbox.slice(2,4))):this.inverse(bbox.slice(0,2)).concat(this.inverse(bbox.slice(2,4)))},SphericalMercator.prototype.forward=function(xy){xy=[A*xy[0]*D2R,A*Math.log(Math.tan(.25*Math.PI+.5*xy[1]*D2R))];return xy[0]>MAXEXTENT&&(xy[0]=MAXEXTENT),xy[0]<-MAXEXTENT&&(xy[0]=-MAXEXTENT),xy[1]>MAXEXTENT&&(xy[1]=MAXEXTENT),xy[1]<-MAXEXTENT&&(xy[1]=-MAXEXTENT),xy},SphericalMercator.prototype.inverse=function(xy){return[xy[0]*R2D/A,(.5*Math.PI-2*Math.atan(Math.exp(-xy[1]/A)))*R2D]},SphericalMercator}();void 0!==module&&void 0!==exports&&(module.exports=exports=SphericalMercator)},{}]},{},[1])(1)});
