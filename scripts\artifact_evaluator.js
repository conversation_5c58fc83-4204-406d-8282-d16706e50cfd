// npm run evaluator
// node artifact_evaluator.js
require('dotenv').config();
const axios = require('axios');
const db = require('./modules/db');
const { ObjectId } = require('mongodb');

const CONFIG = {
    FLASK_API_URL: 'http://localhost:6000',
    COLLECTION_NAME: 'test_db',
    DATABASE: 'qm',
    MAX_ARTIFACTS: 100,
};

async function main() {
    const dbConnection = CONFIG.DATABASE === 'qmai' ? db.qmai : db.qm;
    
    // Fetch artifacts
    const artifacts = await dbConnection.collection(CONFIG.COLLECTION_NAME)
        .find({}, { sort: { timestamp: 1 } })
        .toArray();
    
    if (artifacts.length < 2) return;
    
    // Process artifacts
    const response = await axios.post(`${CONFIG.FLASK_API_URL}/evaluate/sequential`, {
        artifacts: artifacts
    });
    
    // Update database
    const bulkOps = response.data.map(result => {
        const { _id, duplicate_index } = result;
        return {
            updateOne: {
                filter: { _id: typeof _id === 'string' ? new ObjectId(_id) : _id },
                update: { $set: { duplicate_index } }
            }
        };
    });
    
    const bulkResult = await dbConnection.collection(CONFIG.COLLECTION_NAME).bulkWrite(bulkOps);
    console.log(`Updated: ${bulkResult.modifiedCount}/${response.data.length}`);
}

// Run when database is connected
const dbConnection = CONFIG.DATABASE === 'qmai' ? db.qmai : db.qm;
dbConnection.once('open', () => main().then(() => process.exit(0)));
dbConnection.on('error', (err) => { console.error(err); process.exit(1); });
