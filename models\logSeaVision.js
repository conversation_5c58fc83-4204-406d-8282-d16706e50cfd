const mongoose = require('mongoose');
const db = require("../modules/db");


const logSeaVisionSchema = new mongoose.Schema({
    submitted_data: {
        type: Object,
    },
    response_data: {
        type: Object,
    },
    success: {
        type: Boolean
    },
    endpoint: {
        type: String,
        enum: ['PostRFDetection', 'PostCameraDetection'],
    },
    environment: {
        type: String,
        enum: ['staging', 'production'],
    },
    created_at: {
        type: Date,
        default: () => new Date().toISOString()
    },
});

const LogSeaVision = db.qm.model('LogSeaVision', logSeaVisionSchema, 'logs_sea_vision');

module.exports = LogSeaVision;
