require('dotenv').config();
const axios = require('axios');
const db = require('../modules/db');
const { ObjectId } = require('mongodb');

const CONFIG = {
    FLASK_API_URL: 'http://localhost:6000',
    COLLECTION_NAME: 'test_db',
    DATABASE: 'qm',
    MAX_ARTIFACTS: 100,
};

async function main() {
    const dbConnection = CONFIG.DATABASE === 'qmai' ? db.qmai : db.qm;

    // Fetch all artifacts sorted by timestamp
    const artifacts = await dbConnection.collection(CONFIG.COLLECTION_NAME)
        .find({}, { sort: { timestamp: 1 } })
        .toArray();

    if (artifacts.length < 2) return;

    // Group artifacts by onboard_vessel_id
    const groupedArtifacts = {};
    artifacts.forEach(artifact => {
        const vesselId = artifact.onboard_vessel_id || 'unknown';
        if (!groupedArtifacts[vesselId]) {
            groupedArtifacts[vesselId] = [];
        }
        groupedArtifacts[vesselId].push(artifact);
    });

    console.log(`📊 Found ${Object.keys(groupedArtifacts).length} vessel groups`);

    let totalUpdated = 0;
    let totalProcessed = 0;

    // Process each vessel group separately
    for (const [vesselId, vesselArtifacts] of Object.entries(groupedArtifacts)) {
        if (vesselArtifacts.length < 2) {
            console.log(`⏭️  Skipping vessel ${vesselId}: only ${vesselArtifacts.length} artifact(s)`);
            continue;
        }

        console.log(`🚢 Processing vessel ${vesselId}: ${vesselArtifacts.length} artifacts`);

        // Call evaluation API for this vessel group
        const response = await axios.post(`${CONFIG.FLASK_API_URL}/evaluate/sequential`, {
            artifacts: vesselArtifacts
        });

        // Update database for this group
        const bulkOps = response.data.map(result => {
            const { _id, duplicate_index } = result;
            return {
                updateOne: {
                    filter: { _id: typeof _id === 'string' ? new ObjectId(_id) : _id },
                    update: { $set: { duplicate_index } }
                }
            };
        });

        const bulkResult = await dbConnection.collection(CONFIG.COLLECTION_NAME).bulkWrite(bulkOps);
        console.log(`✅ Vessel ${vesselId}: Updated ${bulkResult.modifiedCount}/${response.data.length}`);

        totalUpdated += bulkResult.modifiedCount;
        totalProcessed += response.data.length;
    }

    console.log(`🎯 Total: Updated ${totalUpdated}/${totalProcessed} artifacts across all vessels`);
}

// Run when database is connected
const dbConnection = CONFIG.DATABASE === 'qmai' ? db.qmai : db.qm;
dbConnection.once('open', () => main().then(() => process.exit(0)));
dbConnection.on('error', (err) => { console.error(err); process.exit(1); });
