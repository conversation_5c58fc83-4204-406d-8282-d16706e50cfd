name: Quartermaster Microservices CI

on:
  push:
    branches: [ "main" ]

jobs:
  build:

    runs-on: self-hosted
    permissions:
      contents: read
      actions: read
      statuses: write
    strategy:
      matrix:
        node-version: [18.x ]
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

##############################################################################################
############################## Quartermaster MicroServices CI ################################
##############################################################################################

    - name:
        Deploy to microservices.quartermaster.us - Navigate to Directory
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploying to microservices.quartermaster.us"
        cd ~/quartermaster-web-microservices/
        git restore .
        git pull

    - name:
        Deploy to microservices.quartermaster.us - Install Backend Dependencies
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-web-microservices/
        npm install

    - name:
          Deploy to microservices.quartermaster.us - Clean and Create .env File
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-web-microservices/
        rm -rf .env
        touch .env

    - name:
        Deploy to microservices.quartermaster.us - Populate .env File with Secrets
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-web-microservices/
        echo "${{ secrets.MICROSERVICES_QUARTERMASTER_US }}" > .env

    - name:
        Deploy to microservices.quartermaster.us - Restart PM2 Process
      if: github.ref == 'refs/heads/main'
      run: |
        pm2 restart 'Quartermaster Webservices'

##############################################################################################
################################ Update Slack  ###############################################
##############################################################################################
    
    - name: Set URL based on branch
      run: |
        if [ "${{ github.ref }}" == "refs/heads/main" ]; then
          echo "URL=https://microservices.quartermaster.us" >> $GITHUB_ENV
        else
          echo "URL=https://microservices.quartermaster.us" >> $GITHUB_ENV
        fi

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: '*********************************************************************************'

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: '*********************************************************************************'