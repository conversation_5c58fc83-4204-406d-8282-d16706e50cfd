const nodemailer = require('nodemailer');

/** Note: Porkbun does not currently offer oAuth based authentication so at the moment we are using Basic authentication */

const porkbunTransporter = nodemailer.createTransport({
    host: 'smtp.porkbun.com',
    port: 465,
    secure: true,
    auth: {
        user: process.env.PORKBUN_MAIL_USERNAME,
        pass: process.env.PORKBUN_MAIL_PASSWORD
    },
});

function sendEmail(to, subject, html) {
    return porkbunTransporter.sendMail({
        from: `"${process.env.NODE_ENV === 'localhost' ? 'TEST ' : ''}Quartermaster System" <${process.env.PORKBUN_MAIL_USER}>`,
        to,
        subject,
        html,
    });
}

module.exports = {
    sendEmail
}

/** old <NAME_EMAIL> */

// const { google } = require('googleapis');

// const oAuth2Client = new google.auth.OAuth2(
//     process.env.CLIENT_ID,
//     process.env.CLIENT_SECRET,
//     process.env.REDIRECT_URI
// );

// oAuth2Client.setCredentials({ refresh_token: process.env.REFRESH_TOKEN });

// const { token: accessToken } = await oAuth2Client.getAccessToken();

// const transport = nodemailer.createTransport({
//     service: 'gmail',
//     auth: {
//         type: 'OAuth2',
//         user: process.env.MAIL_USER,
//         clientId: process.env.CLIENT_ID,
//         clientSecret: process.env.CLIENT_SECRET,
//         refreshToken: process.env.REFRESH_TOKEN,
//         accessToken: accessToken,
//     },
// });