const axios = require('axios');
const BASE64_TEST_DATA = require('../utils/base64TestData');

const SAMPLE_REQUEST_INTERVAL = 300000

const aisApi = axios.create({
    baseURL: process.env.SEAVISION_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.SEAVISION_AIS_TOKEN
    }
});

const rfApi = axios.create({
    baseURL: process.env.SEAVISION_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.SEAVISION_RF_TOKEN
    }
});

const cameraApi = axios.create({
    baseURL: process.env.SEAVISION_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.SEAVISION_CAMERA_TOKEN
    }
});

async function submitSampleRF() {
    return await rfApi.post(`/posits`, [
        {
            "source": "RF",
            "trackingCode": "681b33c81f8711a72d1d89e8",
            "lat": 15.830328999999999,
            "lon": 118.04459469999999,
            "time": new Date().toISOString(),
            "mmsi": 0,
            "attributes": {
                "det_conf": 0.66259765625,
                "det_nbbox": "{\"x1\":0.5920711040496827,\"y1\":0,\"x2\":0.9708456039428711,\"y2\":0.9970370822482639}",
                "det_nbbox_area": 0.37765222220355,
                "category": null,
                "super_category": null,
                "text_extraction": "[]",
                "color": "White and blue",
                "size": null,
                "weapons": null,
                "country_flag": null,
                "others": null
            }
        }
    ]).then(res => res.data)
}

async function submitSampleCamera() {
    const file = BASE64_TEST_DATA
    return await cameraApi.post(`/posits/camera`, {
        "lat": 8.6359774,
        "lon": -83.1668541,
        "time": new Date().toISOString(),
        "platform": "QSX0003",
        "file": file,
        "attributes": {
            "camera_id": 0,
            "camera_model": "Q6225-LE",
            "zoom": 2.3796886905789396,
            "brightness": 50,
            "contrast": 60,
            "sharpness": 60,
            "format": "jpg"
        },
        "name": "QSX0003_2025-05-05T13:20:15.925Z"
    }).then(res => res.data)
}

async function submitSampleAIS() {
    return await aisApi.post(`/posits/ais`, [
        {
            "nmeaSentence": "!AIVDM,1,1,,A,6Mn46C?>sVUD025ND8e:br0002S8,2*66",
            "time": new Date().toISOString()
        }
    ]).then(res => res.data)
}

async function executeSampleRequests() {
    console.log('[executeSampleRequests] Submitting SeaVision sample requests...')
    // await submitSampleRF().then((res) => console.log('[executeSampleRequests] Successfully submitted SeaVision RF Sample Request', res)).catch(console.error)
    // await submitSampleCamera().then((res) => console.log('[executeSampleRequests] Successfully submitted SeaVision Camera Sample Request', res)).catch(console.error)
    await submitSampleAIS().then((res) => console.log('[executeSampleRequests] Successfully submitted SeaVision AIS Sample Request', res)).catch(console.error)
    console.log('[executeSampleRequests] SeaVision sample requests submission complete')

    setTimeout(executeSampleRequests, SAMPLE_REQUEST_INTERVAL);
}

setTimeout(executeSampleRequests, SAMPLE_REQUEST_INTERVAL);