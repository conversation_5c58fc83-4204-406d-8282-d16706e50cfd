# Prevent __pycache__ creation
import os
os.environ['PYTHONDONTWRITEBYTECODE'] = '1'
from flask import Flask, request, jsonify
from flask_cors import CORS
from evaluator import Evaluator
from datetime import datetime

app = Flask(__name__)
CORS(app)
evaluator = Evaluator()
@app.route('/evaluate/sequential', methods=['POST'])
def evaluate_sequential():
    print("📡 evaluate_sequential called")
    artifacts = request.get_json()['artifacts']
    print(f"📥 Received {len(artifacts)} artifacts for evaluation")
    results = []
    for i in range(len(artifacts) - 1):
        curr_artifact = artifacts[i]
        next_artifact = artifacts[i + 1]

        same_image_path = curr_artifact.get('image_path') == next_artifact.get('image_path')
        curr_time = datetime.fromisoformat(curr_artifact['timestamp'].replace('Z', '+00:00'))
        next_time = datetime.fromisoformat(next_artifact['timestamp'].replace('Z', '+00:00'))
        time_diff_minutes = abs((next_time - curr_time).total_seconds() / 60)
        same_vessel = curr_artifact.get('onboard_vessel_id') == next_artifact.get('onboard_vessel_id')  

        # Set duplicate_index based on conditions
        if same_image_path or time_diff_minutes > 30 or not same_vessel:
            eval_results = {}
            eval_results['duplicate_index'] = 0
            eval_results['_id'] = artifacts[i + 1]['_id']
            results.append(eval_results)
            print(f"🔄 Pair {i}: duplicate_index=0 (same_path={same_image_path}, time_diff={time_diff_minutes > 30}, same_vessel={same_vessel}, curr_time={curr_time}, next_time={next_time})")
        else:
            eval_results = evaluator.evaluate(curr_artifact, next_artifact)
            eval_results['duplicate_index'] = sum(eval_results.values()) / len(eval_results)
            print(f"🔄 Pair {i}: duplicate_index={eval_results['duplicate_index']:.3f}")
            eval_results['_id'] = artifacts[i + 1]['_id']
            results.append(eval_results)
    print(f"✅ Returning {len(results)} evaluation results")
    return jsonify(results)

if __name__ == '__main__':
    print("🚀 Starting Evaluator API on http://0.0.0.0:6000")
    print("📋 Available endpoints:")
    print("  POST /evaluate/sequential - Evaluate artifacts sequentially")
    app.run(host='0.0.0.0', port=6000, debug=True)
