const db = require('../modules/db');
const { createLoggerWithPath } = require('../modules/winston');
const { getPreviousWeekDateTimeUTC, getPastDaylightDateTimeUTC, getPreviousMonthDateTimeUTC,
    generateToken, buildUrl, generateUnsubscribeToken, dateToString,
    defaultDateTimeFormat
} = require('../utils/functions');
const { NOTIFICATION_SUMMARY_EMAIL_CONTENT } = require("../utils/Email");
const { sendEmail } = require("../modules/email");
const { schedule } = require("node-cron");
const { default: mongoose } = require('mongoose');
const { testMode_mock_data } = require('../utils/notificartionAlertMockData');
const { getClustersAndPoints, mapDefaultOptions } = require("../utils/staticMap");
const User = require("../models/User");
const crypto = require('crypto');
const stableStringify = require('json-stable-stringify');
const { getAuthorizedArtifacts } = require("../services/artifacts");

const logger = createLoggerWithPath('aggregate_statistics')

async function preprocessReportRequests(summaryReports, reportType) {
    const validReportRequests = [];
    const cachedUsers = {};
    const dateFormats = {};

    // INFO: collect only valid reports - with available recipients
    for (const summaryReport of summaryReports) {
        if (!cachedUsers[summaryReport.created_by]) {
            const user = await User.aggregate([
                { $match: { _id: summaryReport.created_by } },
                {
                    $lookup: {
                        from: 'roles',
                        localField: 'role_id',
                        foreignField: 'role_id',
                        as: 'role'
                    }
                },
                { $unwind: '$role' },
            ]);
            cachedUsers[summaryReport.created_by] = user[0];
        }
        const user = cachedUsers[summaryReport.created_by];
        const createdByEmail = user?.email;
        dateFormats[createdByEmail] = user?.date_time_format || defaultDateTimeFormat;

        if (createdByEmail) {
            summaryReport.receivers.push(createdByEmail);
        }

        if (summaryReport.receivers.length === 0) {
            // TODO: deactivate the report if the target user not found and summaryReport.receivers is empty
            // await db.qm.collection('notifications_summary').updateOne({_id: summaryReport._id}, {is_enabled: false });
            logger.info(`[${reportType} procSummaryReports] error generating email content for vessels ${summaryReport.vessel_ids}`)
        } else {
            validReportRequests.push({ summaryReport, user });
        }
    }
    return { validReportRequests, dateFormats };
}

async function procSummaryReports(startTimestamp, endTimestamp, summaryReports, reportType, testMode) {
    logger.info(`[${reportType} procSummaryReports] invoked`)

    const dedupUserEmails = {}
    const emailLogs = [];
    const procDate = Date.now();

    const { validReportRequests, dateFormats } = await preprocessReportRequests(summaryReports, reportType);

    let allVesselIds = validReportRequests.flatMap(r => r.summaryReport.vessel_ids || []);
    allVesselIds = [...new Set(allVesselIds)];

    const query = {
        timestamp: { $gte: new Date(startTimestamp), $lte: new Date(endTimestamp) },
        vessel_presence: true,
        super_category: { $ne: null },
    }

    if (allVesselIds.length) {
        query.onboard_vessel_id = { $in: allVesselIds }
    }

    const artifactsCollection = db.qmai.collection('analysis_results').find(query).addCursorFlag('noCursorTimeout', true);
    const groupedArtifacts = {};
    const sensorShipMapping = {};

    for await (const artifact of artifactsCollection) {
        if (artifact.onboard_vessel_name === 'Unregistered') {
            continue;
        }

        (groupedArtifacts[artifact.onboard_vessel_id] ||= []).push(artifact);
        sensorShipMapping[artifact.onboard_vessel_id] = artifact.onboard_vessel_name;
    }

    if (!Object.keys(groupedArtifacts).length && !testMode) {
        logger.info(`[${reportType} procSummaryReports] no artifacts found for processing`)
        return;
    }

    const allArtifacts = Object.values(groupedArtifacts).flat();
    for (const { summaryReport, user } of validReportRequests) {
        const authorizedArtifacts = await getAuthorizedArtifacts(user, summaryReport, allArtifacts);
        const superCategories = {};
        const countryFlags = {};
        const vesselArtifacts = [];
        let locations = [];
        let amountOfRecords = 0;
        const vesselGroups = {};

        for (const artifact of authorizedArtifacts) {
            const vesselId = artifact.onboard_vessel_id;
            (vesselGroups[vesselId] ||= []).push(artifact);
            superCategories[artifact.super_category || 'Unspecified category'] = (superCategories[artifact.super_category || 'Unspecified category'] ?? 0) + 1;
            const flag = artifact.country_flag || 'Unrecognized';
            countryFlags[flag] = (countryFlags[flag] ?? 0) + 1;
            if (Array.isArray(artifact.location?.coordinates) && artifact.location.coordinates.length > 1) {
                locations.push({ lat: artifact.location.coordinates[1], lng: artifact.location.coordinates[0] });
            }
        }

        for (const vesselId in vesselGroups) {
            vesselArtifacts.push({ name: sensorShipMapping[vesselId], value: vesselGroups[vesselId].length });
            amountOfRecords += vesselGroups[vesselId].length;
        }


        console.info(`[${reportType} procSummaryReports] superCategories: ${JSON.stringify(superCategories)}`)
        const superCategoriesString = Object.entries(superCategories).map(([key, value]) => `${key}: ${value}`).join(', ');

        console.info(`[${reportType} procSummaryReports] superCategoriesString: ${superCategoriesString}`)
        const totalVesselsSuper = Object.values(superCategories).reduce((sum, value) => sum += value, 0);

        console.info(`[${reportType} procSummaryReports] countryFlags: ${JSON.stringify(countryFlags)}`)
        const countryFlagsString = Object.entries(countryFlags).map(([key, value]) => `${key}: ${value}`).join(', ');

        console.info(`[${reportType} procSummaryReports] countryFlagsString: ${countryFlagsString}`)
        const totalVesselsCountry = Object.values(countryFlags).reduce((sum, value) => sum += value, 0);

        const payload = {
            type: reportType,
            superCtg: `${totalVesselsSuper} Vessels (${superCategoriesString})`,
            flags: `${totalVesselsCountry} Vessels (${countryFlagsString})`,
            incursions: null, // TODO: should be integer
            highDetection: null, // TODO: should be integer
            totalDetection: amountOfRecords,
            vessels: vesselArtifacts,
        }

        // INFO: processing locations
        const markers = await getClustersAndPoints(locations, mapDefaultOptions);
        const token = markers?.length ? generateToken({ markers }) : undefined;
        // INFO: we should check the length of the token because we limited by GET request restrictions (8K size)
        let mapUrl = token && token.length < 8000 ? buildUrl('/v2/summaryReports/map', { token }) : null;
        if (testMode && !mapUrl) {
            mapUrl = testMode_mock_data.mapUrl
        }
        console.info(`[${reportType} procSummaryReports] MAP URL attached: ${mapUrl ? mapUrl : 'No map url'}`)
        for (const receiver of summaryReport.receivers) {
            const stringifiedPayload = stableStringify(payload);
            const hash = crypto.createHash('sha256').update(stringifiedPayload).digest('hex');

            if (dedupUserEmails[receiver] && dedupUserEmails[receiver].includes(hash)) {
                console.info(`[${reportType} procSummaryReports] duplicate email detected, skipping...`)
                continue;
            }

            (dedupUserEmails[receiver] ||= []).push(hash);

            const dateFormat = dateFormats[receiver];

            const unsubscribeUrl = buildUrl('/summaryReports/unsubscribe/email', { token: generateUnsubscribeToken(receiver, summaryReport._id) })
            const email = NOTIFICATION_SUMMARY_EMAIL_CONTENT(payload, dateToString(procDate, dateFormat, true), unsubscribeUrl, mapUrl);
            let mailSent = true;

            try {
                await sendEmail(receiver, `${reportType} Vessel Detection Report`, email)
            } catch (err) {
                console.error(`[procSummaryReports] Error while sending email: ${err}`);
                mailSent = false;
            }

            const log = {
                subject: `${reportType} Vessel Detection Report`,
                receivers: receiver,
                environment: process.env.NODE_ENV,
                type: `${reportType.toLowerCase()}_summary`,
                data: {
                    unit_id: summaryReport.unit_id,
                    vessel_ids: summaryReport.vessel_ids,
                    map_url: mapUrl,
                },
                delivered: !!mailSent,
                created_at: new Date().toISOString()
            }

            emailLogs.push(log)
            console.info(`[${reportType} procSummaryReports] email sent : \n`)
        }
    }
    if (emailLogs.length > 0) {
        const response = await db.qm.collection('logs_emails').insertMany(emailLogs);
        console.info(`[${reportType} procSummaryReports] email logs inserted: ${response.insertedCount}`)
    }
}

async function generateDailySummaryReports(options = {}) {
    const { userId, testMode } = options;
    logger.info('[generateDailySummaryReports] invoked')
    const query = {
        preference: { $in: ['daily'] },
        is_enabled: true
    }
    if (testMode) {
        query.created_by = new mongoose.Types.ObjectId(userId)
    }
    const summaryReports = await db.qm.collection('notifications_summary').find(query).toArray();

    if (summaryReports.length === 0) {
        logger.info('[generateDailySummaryReports] no daily summary reports to generate')
        return
    }

    const { start: startTimestamp, end: endTimestamp } = getPastDaylightDateTimeUTC()

    await procSummaryReports(startTimestamp, endTimestamp, summaryReports, 'Daily', testMode)

    logger.info('[generateDailySummaryReports] completed for day', new Date().toISOString())
}

async function generateWeeklySummaryReports(options = {}) {
    const { userId, testMode } = options;
    logger.info('[generateWeeklySummaryReports] invoked')
    const query = {
        preference: { $in: ['weekly'] },
        is_enabled: true
    }
    if (testMode) {
        query.created_by = new mongoose.Types.ObjectId(userId)
    }
    const summaryReports = await db.qm.collection('notifications_summary').find(query).toArray();

    if (summaryReports.length === 0) {
        logger.info('[generateWeeklySummaryReports] no weekly summary reports to generate')
        return
    }

    const { start: startTimestamp, end: endTimestamp } = getPreviousWeekDateTimeUTC()

    await procSummaryReports(startTimestamp, endTimestamp, summaryReports, 'Weekly', testMode)

    logger.info('[generateWeeklySummaryReports] completed for week', new Date().toISOString())
}

async function generateMonthlySummaryReports(options = {}) {
    const { userId, testMode } = options;
    logger.info('[generateMonthlySummaryReports] invoked');
    const query = {
        preference: { $in: ['monthly'] },
        is_enabled: true
    }
    if (testMode) {
        query.created_by = new mongoose.Types.ObjectId(userId)
    }
    const summaryReports = await db.qm.collection('notifications_summary').find(query).toArray();

    if (!summaryReports.length) {
        logger.info('[generateMonthlySummaryReports] no monthly summary reports to generate');
        return;
    }

    const { start: startTimestamp, end: endTimestamp } = getPreviousMonthDateTimeUTC();

    await procSummaryReports(startTimestamp, endTimestamp, summaryReports, 'Monthly', testMode)

    logger.info('[generateMonthlySummaryReports] completed for month', new Date().toISOString());
}

logger.info('setting init timers for statistics computation')

schedule('1 0 12 * * *', function () { generateDailySummaryReports({}) }, { scheduled: true, timezone: 'UTC' }) // at 12:00:01 each day
schedule('1 0 16 * * Sunday', function () { generateWeeklySummaryReports({}) }, { scheduled: true, timezone: 'UTC' }) // at 16:00:01 each Sunday
schedule('1 0 0 1 * *', function () { generateMonthlySummaryReports({}) }, { scheduled: true, timezone: 'UTC' }) // at 00:00:01 on the 1st day

module.exports = {
    generateDailySummaryReports,
    generateWeeklySummaryReports,
    generateMonthlySummaryReports
}
