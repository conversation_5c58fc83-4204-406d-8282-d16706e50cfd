# Evaluator API

Flask API for artifact evaluation using the evaluator.py script.

## Setup

### Install Dependencies
```bash
# Install Python dependencies
make install
# or
pip install -r requirements.txt
```

### Development Setup
```bash
# Install in development mode
make dev
# or
pip install -e .
```

## Usage

### Start the API Server
```bash
# Production mode
make start
# or
python flask_evaluator_api.py

# Development mode (with hot reload)
make dev-start
```

### API Endpoints

#### POST /evaluate/sequential
Evaluates artifacts sequentially (prev vs next comparison).

**Request:**
```json
{
  "artifacts": [
    {
      "_id": "...",
      "timestamp": "...",
      "image_path": "...",
      "category": "...",
      "super_category": "...",
      "size": "...",
      "imo_number": "...",
      "color": "...",
      "weapons": "...",
      "country_flag": "...",
      "others": "...",
      "text_extraction": [...]
    }
  ]
}
```

**Response:**
```json
[
  {
    "_id": "artifact_id",
    "duplicate_index": 0.856,
    "other_evaluation_scores": "..."
  }
]
```

### Duplicate Detection Logic
- `duplicate_index = 0` when:
  - Same image_path OR
  - Time difference > 30 minutes
- Otherwise: `duplicate_index = average of evaluation scores`

## Commands

```bash
make install     # Install dependencies
make dev         # Development setup
make start       # Start API server
make dev-start   # Start with hot reload
make clean       # Clean cache files
make test        # Run tests
```
