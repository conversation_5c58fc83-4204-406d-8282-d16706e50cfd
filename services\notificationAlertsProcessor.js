const { default: mongoose } = require("mongoose");
const {
    dateToString,
    generateToken,
    buildUrl,
    generateUnsubscribeToken, defaultDateTimeFormat
} = require("../utils/functions");
const db = require("../modules/db");
const { s3 } = require("../modules/awsS3");
const { ARTIFACT_NOTIFICATION_EMAIL_CONTENT } = require("../utils/Email");
const { sendEmail } = require("../modules/email");
const { default: getCountryFlag } = require('country-flag-icons/unicode');
const { testMode_mock_data } = require("../utils/notificartionAlertMockData");
const eventEmitter = require("../modules/eventEmitter");
const User = require("../models/User");
const { displayCoordinates } = require("../utils/functions");
const { getAuthorizedArtifacts } = require("../services/artifacts");

let flagsMappings = undefined;

async function prepareFlagsMapping() {
    if (!flagsMappings) {
        const countryFlags = await db.qm.collection("notification_flags").find().toArray();
        flagsMappings = {};
        countryFlags.forEach(flag => { flagsMappings[flag.name] = flag.code; });
    }

    return flagsMappings;
}

const getFlagPict = async (countryName) => {
    const code = (await prepareFlagsMapping())[countryName];

    if (code) {
        return getCountryFlag(code);
    }
}

async function getLastProcessedArtifactData(unitId) {
    if (!unitId) return undefined;
    return (await db.qmai.collection('processing_cache').findOne({ unit_id: unitId }));
}

async function setLastProcessedArtifactData(unitId, data) {
    await db.qmai.collection('processing_cache')
        .updateOne({ unit_id: unitId }, { $set: { unit_id: unitId, ...data } }, { upsert: true })
}

const isAdequateArtifact = async (artifact, testMode) => {
    let currArtifact = artifact;

    try {
        if (typeof currArtifact === 'string') {
            currArtifact = await db.qmai.collection('analysis_results').findOne({ _id: new mongoose.Types.ObjectId(artifact) })
            if (!currArtifact) throw new Error('Artifact does not exist')
        }

        console.log('currArtifact', currArtifact)

        // Mahsam: There's now a possibility to take images in either zoomed-in and zoomed-out or both. So it is not necessarily a duplicate
        // const metadata = await s3.getObject({
        //     Bucket: currArtifact.bucket_name,
        //     Key: currArtifact.metadata_path
        // }).promise().then((data) => JSON.parse(data.Body.toString("utf-8")))

        // if (metadata.camera_id === 0) {
        //     console.log('[isAdequateArtifact] Camera is zoomed')
        //     return false
        // }

        if (!currArtifact.vessel_presence) { // || !currArtifact.super_category
            console.log('[isAdequateArtifact] No vessel presence')
            return false
        }

        if (!currArtifact.super_category) {
            console.log('[isAdequateArtifact] super_category is null or undefined')
            return false;
        }

        if (currArtifact.det_nbbox_area < 0.03) { // || !currArtifact.super_category
            console.log('[isAdequateArtifact] det_nbbox_area is less than 0.03')
            return false
        }

        const prevArtifact = await getLastProcessedArtifactData(currArtifact.unit_id);
        console.log('prevArtifact', prevArtifact)

        if (prevArtifact && !testMode) {
            if ((new Date(currArtifact.timestamp).getTime() - new Date(prevArtifact.timestamp).getTime()) <= 1000) {
                console.log('[isAdequateArtifact] Time difference is less than 1s')
                return false
            }
        }

        await setLastProcessedArtifactData(currArtifact.unit_id, { timestamp: new Date(currArtifact.timestamp).getTime() })

        return true;
    } catch (err) {
        throw err
    }
}

function buildEmailLog(email) {
    return {
        subject: email.subject,
        receiver: email.email_receivers,
        environment: process.env.NODE_ENV,
        delivered: email.emailSent,
        type: 'artifact',
        data: {
            super_category: email.artifact.super_category,
            sub_category: email.artifact.category,
            artifact_id: email.artifact._id,
        },
        created_at: new Date().toISOString(),
    }
}

async function processNotificationAlerts({ artifacts, userId, testMode }) {
    const adequateArtifacts = [];
    const dateFormats = {};
    const coordinatesFormatPreferences = {};
    const procDate = Date.now();

    for (let artifact of artifacts) {
        try {
            if (await isAdequateArtifact(artifact, testMode)) {
                adequateArtifacts.push(artifact);
            }
        } catch (err) {
            console.error(`Error while checking artifact adequacy: ${err}`);
        }
    }

    if (adequateArtifacts.length <= 0) {
        console.info('[Dispatch Notifications]: No adequate artifacts found returning');
        return;
    }

    console.info(`[Dispatch Notifications]: Artifacts: ${adequateArtifacts.map(artifact => artifact._id).join(', ')}`);
    const categories = [...new Set(adequateArtifacts.map(artifact => artifact.category).filter(Boolean))];

    console.info(`Unique Categories: ${categories}`);
    const super_categories = [...new Set(adequateArtifacts.map(artifact => artifact.super_category).filter(Boolean))];
    console.info(`Unique Super Categories: ${super_categories}`);

    const flags = [...new Set(adequateArtifacts.map(artifact => artifact.flags).filter(Boolean))];
    console.info(`Unique Flags: ${flags}`);

    const filters = {
        $and: [
            { is_enabled: true },
            {
                $or: [
                    { sub_category: { $elemMatch: { $in: categories } } },
                    { super_category: { $elemMatch: { $in: super_categories } } },
                    { country_flags: { $elemMatch: { $in: flags } } },
                    { country_flags: { $size: 0 } }
                ]
            }
        ]
    };
    if (testMode) {
        filters.created_by = new mongoose.Types.ObjectId(userId);
    }

    const alerts = await db.qm
        .collection('notifications_alerts')
        .find(filters)
        .toArray();

    console.info(`Alerts: ${alerts.map(alert => alert._id).join(', ')}`);
    if (alerts.length <= 0) {
        console.warn('[Dispatch Notifications]: No alerts found in DB to be dispatched');
        return;
    }

    /** for v2 purposes
     // const filteredAlerts = alerts.filter(alert => alert.receivers.length > 0);
     //
     // if (filteredAlerts.length <= 0) {
     //     console.info('[Dispatch Notifications]: No alerts because no receivers found');
     //     return;
     // }
     */

    const data = [];
    const userCache = {};

    for (const alert of alerts) {
        if (!userCache[alert.created_by]) {
            const user = await User.aggregate([
                { $match: { _id: alert.created_by } },
                {
                    $lookup: {
                        from: 'roles',
                        localField: 'role_id',
                        foreignField: 'role_id',
                        as: 'role'
                    }
                },
                { $unwind: '$role' },
            ]);
            userCache[alert.created_by] = user[0];
        }
        const targetUser = userCache[alert.created_by];

        const authorizedArtifacts = await getAuthorizedArtifacts(
            targetUser,
            alert,
            adequateArtifacts
        );
        if (!authorizedArtifacts.length) continue;

        for (const artifact of authorizedArtifacts) {
            dateFormats[targetUser?.email] = targetUser?.date_time_format ?? defaultDateTimeFormat;
            coordinatesFormatPreferences[targetUser?.email] = targetUser?.use_MGRS || false;

            if (!alert.super_category.length || !alert.super_category.includes(artifact.super_category)) {
                console.info(`[Dispatch Notifications]: Artifact super_category ${artifact.super_category} does not match alert super_category ${alert.super_category} returning`);
                continue;
            }

            if (!alert.sub_category.length || !alert.sub_category.includes(artifact.category)) {
                console.info(`[Dispatch Notifications]: Artifact sub_category ${artifact.sub_category} does not match alert sub_category ${alert.sub_category} returning`);
                continue;
            }

            if (!alert.country_flags.length || (alert.country_flags[0] !== 'all' && !alert.country_flags.includes(artifact.country_flag))) {
                console.info(`[Dispatch Notifications]: Artifact country_flags ${artifact.country_flag} does not match alert country_flags ${alert.country_flags} returning`);
                continue;
            }

            data.push({
                alert: alert,
                artifact: artifact,
                receiver: alert.created_by,
                email_receivers: alert.receivers,
                type: alert.type,
            });
        }
    }

    if (data.length <= 0) {
        console.info('[Dispatch Notifications]: No data found to be dispatched: ', data.length);
        return;
    }

    // console.info(`[Dispatch Notifications]: Dispatching data: ${JSON.stringify(uniqueData)}`);

    const notificationPayload = [];
    const emailPayload = [];
    const artefactImageUrlLifetime = 60 * 60 * 24 * 7;

    for (const d of data) {
        if (d.type === 'email' || d.type === 'both') {
            const emailReceivers = [...new Set([userCache[d.receiver].email, ...d.email_receivers])]

            if (!emailReceivers.length) {
                console.info(`[Dispatch Notifications]: No email receivers found`);
                continue;
            }

            const params = {
                Bucket: d.artifact.bucket_name,
                Key: d.artifact.image_path,
                Expires: artefactImageUrlLifetime,
            };
            s3.config.update({ region: d.artifact.aws_region, signatureVersion: 'v4' })
            d.artifact.signedUrl = s3.getSignedUrl('getObject', params);

            emailPayload.push({
                subject: d.artifact?.super_category ? `New Artifacts detected: ${d.artifact.super_category}` : 'New Artifacts detected',
                message: d.artifact.description || 'no description',
                email_receivers: emailReceivers,
                artifact: d.artifact,
                alertId: d.alert._id,
            });
        }

        if (d.type === 'app' || d.type === 'both') {
            notificationPayload.push({
                title: d.artifact.super_category || 'Unknown',
                message: d.artifact.description || 'no description',
                receiver: d.receiver,
                artifact_id: d.artifact._id,
                is_read: false,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
            });
        }
    }

    if (notificationPayload.length) {
        //bulk insert
        await db.qm.collection('in_app_notifications').insertMany(notificationPayload);
        eventEmitter.emit('alert/notify', { total: notificationPayload.length });
    } else {
        console.info('[Dispatch Notifications]: No notifications found to be dispatched length 0');
    }

    if (emailPayload.length <= 0) {
        console.info('[Dispatch Notifications]: No emails found to be dispatched length 0');
        return;
    }

    console.info(`[Dispatch Notifications]: Dispatching emailPayload: ${JSON.stringify(emailPayload)}`);
    console.info(`[Dispatch Notifications]: Dispatching notificationPayload: ${JSON.stringify(notificationPayload)}`);

    const emailLogs = [];

    await Promise.all(emailPayload.map(async (email, index) => {
        if (email.email_receivers.length <= 0) {
            console.info(`[Dispatch Notifications]: No email receivers found for email ${index}`);
            email.emailSent = false;
            return email;
        }

        let mapUrl = undefined;

        if (Array.isArray(email.artifact.location?.coordinates) && email.artifact.location.coordinates.length > 1) {
            const mapToken = generateToken({ locations: [[email.artifact.location.coordinates[1], email.artifact.location.coordinates[0]]] });
            mapUrl = buildUrl('/notificationsAlerts/map', { token: mapToken })
            if (testMode && !mapUrl) {
                mapUrl = testMode_mock_data.mapUrl
            }
        } else {
            console.warn(`[Dispatch Notifications] No location in the artefact: ${email.artifact._id}`)
        }

        console.info(`[Dispatch Notifications] MAP URL attached: ${mapUrl ? mapUrl : 'No map url'}`)
        const flagPict = await getFlagPict(email.artifact?.country_flag);

        // INFO: notification's creator will be a regulator of the date format and the use of MGRS
        const dateFormat = dateFormats[email.email_receivers[0]];
        const useMGRS = !!coordinatesFormatPreferences[email.email_receivers[0]];

        for (const email_receiver of email.email_receivers) {
            const unsubscribeUrl = buildUrl('/notificationsAlerts/unsubscribe/email', { token: generateUnsubscribeToken(email_receiver, email.alertId) })
            const coordinates = email.artifact?.location?.coordinates ? displayCoordinates(email.artifact.location.coordinates, useMGRS) : null

            email.page = ARTIFACT_NOTIFICATION_EMAIL_CONTENT(email.artifact, coordinates, dateToString(procDate, dateFormat, true), unsubscribeUrl, flagPict, mapUrl);

            try {
                await sendEmail(email_receiver, email.subject, email.page);
                email.emailSent = true;
            } catch (err) {
                console.error(`[processEmailsAndNotifications] Error while sending email: ${err}`);
                email.emailSent = false;
            }

            emailLogs.push(buildEmailLog(email));
        }
    }));

    await db.qm.collection('logs_emails').insertMany(emailLogs);

    console.info(`[Dispatch Notifications]: sending emails inserted  logs_emails: ${emailLogs.map(log => log.receiver).join(', ')}`);
}

module.exports = {
    processNotificationAlerts,
};