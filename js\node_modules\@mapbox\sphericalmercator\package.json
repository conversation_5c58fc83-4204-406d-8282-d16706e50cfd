{"name": "@mapbox/sphericalmercator", "description": "Transformations between the Web Mercator projection and Latitude Longitude coordinates", "version": "1.2.0", "licenses": [{"type": "BSD"}], "keywords": ["map", "projection", "transformations"], "url": "http://github.com/mapbox/sphericalmercator", "repository": {"type": "git", "url": "git://github.com/mapbox/sphericalmercator.git"}, "author": {"name": "MapBox", "url": "http://mapbox.com/", "email": "<EMAIL>"}, "main": "./sphericalmercator", "devDependencies": {"tape": "3.0.x", "eslint": "~1.0.0", "eslint-config-unstyled": "^1.1.0"}, "scripts": {"test": "eslint sphericalmercator.js && tape test/*.test.js"}, "bin": {"bbox": "bin/bbox.js", "xyz": "bin/xyz.js", "to900913": "bin/to900913.js", "to4326": "bin/to4326.js"}}