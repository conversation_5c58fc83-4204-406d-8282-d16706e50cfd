const mongoose = require('mongoose');
const db = require('../modules/db');
const User = require('./User');

const vesselSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    thumbnail_s3_key: {
        type: String,
        required: false,
        default: null,
    },
    unit_id: {
        type: String,
        required: false,
        unique: true,
        sparse: true,
    },
    is_active: {
        type: Boolean,
        required: true,
        default: true,
    },
    units_history: [
        {
            _id: false,
            unit_id: {
                type: String,
                required: true,
            },
            mount_timestamp: {
                type: Date,
                required: true,
            },
            unmount_timestamp: {
                type: Date,
                required: false,
            },
        },
    ],
    creation_timestamp: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
    created_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: User,
        required: true,
    },
});

const Vessel = db.qmShared.model('Vessel', vesselSchema, 'vessels');

module.exports = Vessel;
