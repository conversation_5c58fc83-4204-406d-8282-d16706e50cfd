# CHANGELOG

## v0.5.0

* Update viewport method to take an optional `allowAntiMeridian` parameter

## v0.4.0

* Update viewport method to take an optional `allowFloat` parameter to allow float values (h/t @TeaSeaLancs) [#15](https://github.com/mapbox/geo-viewport/pull/15)
* Remove outdated Node.js version support and test with v6 and v8

## v0.3.0

* Update sphericalmercator to 1.1.0 to support float zooms in bounds method (h/t @timiyay) [#19](https://github.com/mapbox/geo-viewport/pull/19)

## v0.2.2

* Updated spherical mercator dependency to @mapbox namespace
* Changed package to @mapbox namespace
