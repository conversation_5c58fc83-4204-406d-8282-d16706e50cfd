require('dotenv').config();
const db = require('../modules/db');

const BATCH_SIZE = 10000;
const DISTANCE_THRESHOLD = 50;
const WINDOW_SIZE = 4;
const collections = [
    'test_location',
    'prototype-37_location'
];

const populateStationaryCoordinates = async (coordinates) => {
    if (!coordinates || coordinates.length === 0) return [];
    const LatLonSpherical = (await import('geodesy/latlon-spherical.js')).default;

    const enhancedPath = coordinates.map((point, i) => {
        if (i === 0) {
            return { ...point, isStationary: false };
        }

        if (i < WINDOW_SIZE) {
            return { ...point, isStationary: false };
        }

        const currentPoint = new LatLonSpherical(point.latitude, point.longitude);
        const windowPoints = coordinates.slice(i - WINDOW_SIZE, i + 1);

        const isStationary = windowPoints.every((windowPoint, windowIndex) => {
            if (windowIndex === windowPoints.length - 1) return true; // Skip comparing with itself
            const prevPoint = new LatLonSpherical(windowPoint.latitude, windowPoint.longitude);
            const distance = currentPoint.distanceTo(prevPoint);
            return distance <= DISTANCE_THRESHOLD;
        });

        return {
            ...point,
            isStationary: isStationary,
        };
    });

    return enhancedPath;
};

async function processVesselLocations(collection) {
    const startTime = Date.now();
    let totalProcessed = 0;

    console.log(`Processing collection: ${collection.collectionName}`);

    const coordinates = await collection
        .find()
        .sort({ timestamp: 1 })
        .toArray();

    console.log(`Found ${coordinates.length} coordinates to process`);

    const processedCoordinates = await populateStationaryCoordinates(coordinates);

    for (let i = 0; i < processedCoordinates.length; i += BATCH_SIZE) {
        const batch = processedCoordinates.slice(i, i + BATCH_SIZE);

        const updates = batch.map(point => ({
            updateOne: {
                filter: { _id: point._id },
                update: { $set: { isStationary: point.isStationary } }
            }
        }));

        if (updates.length > 0) {
            const result = await collection.bulkWrite(updates);
            totalProcessed += result.modifiedCount;
            const progress = Math.round((i + batch.length) / processedCoordinates.length * 100);
            console.log(`Progress: ${i + batch.length}/${processedCoordinates.length} documents (${progress}%)`);
        }
    }

    const timeElapsed = (Date.now() - startTime) / 1000; // Convert to seconds
    console.log(`Completed processing ${collection.collectionName}: ${totalProcessed} documents updated (took ${timeElapsed.toFixed(2)} seconds)`);
    return totalProcessed;
}

async function migrateStationaryStatus() {
    const startTime = Date.now();
    try {
        console.log('Starting migration...');

        // Get all collections that end with _location
        let totalUpdated = 0;

        // Process each collection
        for (const collectionName of collections) {
            const collection = db.qmLocations.collection(collectionName);
            const updatedCount = await processVesselLocations(collection);
            totalUpdated += updatedCount;
        }

        const timeElapsed = (Date.now() - startTime) / 1000; // Convert to seconds
        console.log(`Migration completed. Total documents updated: ${totalUpdated} (total time: ${timeElapsed.toFixed(2)} seconds)`);
    } catch (error) {
        console.error('Migration failed:', error);
    } finally {
        await db.qm.close();
        process.exit(0);
    }
}

db.qmLocations.once('open', () => {
    console.log('Database connected, starting migration...');
    migrateStationaryStatus()
        .then(() => console.log('Migration completed successfully'))
        .catch(err => console.error('Migration failed:', err));
});