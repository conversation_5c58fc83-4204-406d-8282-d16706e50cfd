require('dotenv').config();
const mongoose = require('mongoose');
const { s3 } = require('../modules/awsS3');
const db = require('../modules/db');




const batchSize = 200;
const delayTime = 4000;


async function updateIsVideoArtifacts(batchSize) {

    let skip = 0;
    let hasMore = true;

    while (hasMore) {
        try {
            const batch = await db.qmai.collection('analysis_results')
                .find({ unit_id: "QMTB000001" })
                .skip(skip)
                .limit(batchSize)
                .toArray();

            if (batch.length === 0) {
                hasMore = false;
                break;
            }

            console.log(`Processing batch ${skip / batchSize + 1}...`);

            const updates = await Promise.all(batch.map(async (doc) => {
                if (!doc.bucket_name || !doc.image_path) {
                    return null;
                }
                const videoPath = doc.image_path.replace('.jpg', '.mp4');

                const params = {
                    Bucket: doc.bucket_name,
                    Key: videoPath.replace('/image/', '/video/'),
                };
                try {
                    const response = await s3.headObject(params).promise();
                    console.info(`Video found: ${JSON.stringify(response)}`);
                    return {
                        updateOne: {
                            filter: { _id: doc._id },
                            update: { $set: { video_path: videoPath.replace('/image/', '/video/') } }
                        }
                    };
                } catch (err) {
                    if (err.code === 'NotFound') {
                        return null;
                    }
                    console.error(`Error while checking video: ${err}`);
                }
            }));


            const validUpdates = updates.filter(update => update !== null);

            if (validUpdates.length > 0) {
                const result = await db.qmai.collection('analysis_results').bulkWrite(validUpdates);
                console.log(`Updated documents: ${JSON.stringify(result)}`);
            }
        } catch (error) {
            console.error('Error during batch processing:', error);
        }

        skip += batchSize;
        await new Promise(resolve => setTimeout(resolve, delayTime));
    }

    await mongoose.disconnect();
}

db.qmai.on('open', () => {
    updateIsVideoArtifacts(batchSize)
        .then(() => console.log('Batch update completed'))
        .catch(err => console.error('Error during batch update:', err));
})