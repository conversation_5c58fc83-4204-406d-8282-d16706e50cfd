#!/bin/bash

# Python API Deployment Script for AWS Linux
# Run this from the python/ directory

set -e

echo "🐍 Deploying Python Evaluator API..."

# Install system dependencies for Python packages
echo "📦 Installing system dependencies..."
sudo yum groupinstall -y "Development Tools"
sudo yum install -y python39-devel gcc gcc-c++ make

# Create virtual environment (recommended for production)
if [ ! -d "venv" ]; then
    echo "🔧 Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
echo "📦 Installing Python dependencies..."
pip install -r requirements.txt

# Install the package in development mode
pip install -e .

echo "✅ Python API setup completed!"
echo ""
echo "🚀 To start the API:"
echo "  source venv/bin/activate"
echo "  python flask_evaluator_api.py"
echo ""
echo "🔧 Or use make commands:"
echo "  make start    - Start the API"
echo "  make install  - Install dependencies"
