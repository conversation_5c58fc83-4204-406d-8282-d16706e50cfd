const axios = require('axios');
const jwt = require('jsonwebtoken');
const ThingsboardDevices = require('../models/ThingsboardDevices');

class ThingsBoardApi {
    constructor() {
        this.host = process.env.THINGSBOARD_API_BASE_URL;
        this.username = process.env.THINGSBOARD_USERNAME;
        this.password = process.env.THINGSBOARD_PASSWORD;
        this.templateDashboardId = process.env.THINGSBOARD_DASHBOARD_ID;
        this.jwtToken = null;
        this.tokenExpiresAt = null;
        this.axiosInstance = axios.create({
            baseURL: this.host,
            headers: { 'Content-Type': 'application/json' },
        });
    }

    async authenticate() {
        const res = await axios.post(`${this.host}/api/auth/login`, {
            username: this.username,
            password: this.password,
        });
        this.jwtToken = res.data.token;
        const decoded = jwt.decode(this.jwtToken);
        this.tokenExpiresAt = decoded.exp * 1000;
        this.axiosInstance.defaults.headers['X-Authorization'] = `Bearer ${this.jwtToken}`;
        return this.jwtToken;
    }

    async getValidToken() {
        if (!this.jwtToken || !this.tokenExpiresAt || Date.now() > this.tokenExpiresAt - 60 * 1000) {
            await this.authenticate();
        }
        return this.jwtToken;
    }

    async createDevice(deviceName) {
        await this.getValidToken();
        const res = await this.axiosInstance.post('/api/device', {
            name: deviceName,
            type: 'default',
        });
        const tbDevice = res.data;
        const accessToken = await this.getDeviceAccessToken(tbDevice.id.id);
        const dashboard = await this.createDashboard(tbDevice.id.id, deviceName);
        await ThingsboardDevices.create({
            deviceId: tbDevice.id.id,
            deviceName,
            accessToken,
            dashboardId: dashboard.id.id
        });
        return tbDevice;
    }

    async getDeviceAccessToken(deviceId) {
        await this.getValidToken();
        const res = await this.axiosInstance.get(`/api/device/${deviceId}/credentials`);
        return res.data.credentialsId;
    }

    async sendTelemetry(accessToken, telemetry) {
        const url = `${this.host}/api/v1/${accessToken}/telemetry`;
        return axios.post(url, telemetry, {
            headers: { 'Content-Type': 'application/json' },
        });
    }

    async createDashboard(deviceId, deviceName) {
        await this.getValidToken();
        const res = await this.axiosInstance.get(`/api/dashboard/${this.templateDashboardId}`);
        const dashboard = res.data;
        // Replace device ID
        if (dashboard.configuration && dashboard.configuration.widgets) {
            Object.keys(dashboard.configuration.widgets).forEach(widgetId => {
                const widget = dashboard.configuration.widgets[widgetId];
                if (widget.config && widget.config.datasources) {
                    if (Array.isArray(widget.config.datasources)) {
                        widget.config.datasources.forEach(datasource => {
                            if (datasource.deviceId) {
                                datasource.deviceId = deviceId;
                            }
                        });
                    } else if (widget.config.datasources.deviceId) {
                        widget.config.datasources.deviceId = deviceId;
                    }
                }
            });
        }

        dashboard.title = dashboard.name = dashboard.configuration.states.default.name = `Dashboard for ${deviceName}`;

        delete dashboard.id;
        delete dashboard.createdTime;

        const newDashboard = await this.axiosInstance.post('/api/dashboard', dashboard);
        return newDashboard.data;
    }

    async resetDashboards() {
        await this.getValidToken();
        const searchQuery = 'Template';
        let page = 0;
        let hasMore = true;
        const pageSize = 100;
        const devices = await ThingsboardDevices.find({}, { deviceId: 1, deviceName: 1, accessToken: 1, dashboardId: 1 });
        const dashboardMap = new Map(devices.map(d => [d.dashboardId, d]));

        while (hasMore) {
            const response = await this.axiosInstance.get(`/api/tenant/dashboards?pageSize=${pageSize}&page=${page}`);
            const { data, hasNext } = response.data;
            for (const dashboard of data) {
                if (!dashboard.title.toLowerCase().includes(searchQuery.toLowerCase())) {
                    const device = dashboardMap.get(dashboard.id.id)
                    if (device) {
                        if (device.dashboardId) {
                            await this.axiosInstance.delete(`/api/dashboard/${device.dashboardId}`);
                        }
                        const newDashboard = await this.createDashboard(device.deviceId, device.deviceName);
                        await ThingsboardDevices.updateOne(
                            { dashboardId: dashboard.id.id },
                            { $set: { dashboardId: newDashboard.id.id } }
                        );
                    }
                }
            }

            hasMore = hasNext;
            page++;
        }
    }

    async bulkCreateDevices(deviceNames) {
        await this.getValidToken();
        const results = [];

        for (const deviceName of deviceNames) {
            try {
                const existingDevice = await ThingsboardDevices.findOne({ deviceName });

                if (!existingDevice) {
                    const tbDevice = await this.createDevice(deviceName);
                    results.push({
                        deviceName,
                        status: 'created',
                        device: tbDevice
                    });
                } else {
                    results.push({
                        deviceName,
                        status: 'exists',
                        device: existingDevice
                    });
                }
            } catch (error) {
                results.push({
                    deviceName,
                    status: 'error',
                    error: error.message
                });
            }
        }

        return results;
    }
}

module.exports = new ThingsBoardApi();