from setuptools import setup, find_packages

setup(
    name="evaluator-api",
    version="1.0.0",
    description="Flask API for artifact evaluation",
    packages=find_packages(),
    install_requires=[
        "Flask==3.1.1",
        "flask-cors==6.0.1",
        "numpy==1.26.4",
        "sentence-transformers==3.3.1"
    ],
    python_requires=">=3.8",
    entry_points={
        'console_scripts': [
            'evaluator-api=flask_evaluator_api:main',
        ],
    },
)
