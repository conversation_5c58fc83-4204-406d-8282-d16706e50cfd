require('dotenv').config();
const axios = require('axios');
const db = require('../modules/db');
const { ObjectId } = require('mongodb');

async function main() {
    // Fetch all artifacts sorted by timestamp
    const artifacts = await db.qmai.collection("analysis_results")
        .find({}, { sort: { timestamp: 1 } })
        .toArray();

    if (artifacts.length < 2) return;

    // Group artifacts by onboard_vessel_id
    const groupedArtifacts = {};
    artifacts.forEach(artifact => {
        const vesselId = artifact.onboard_vessel_id || 'unknown';
        if (!groupedArtifacts[vesselId]) {
            groupedArtifacts[vesselId] = [];
        }
        groupedArtifacts[vesselId].push(artifact);
    });

    console.log(`📊 Found ${Object.keys(groupedArtifacts).length} vessel groups`);

    let totalUpdated = 0;
    let totalProcessed = 0;

    // Process each vessel group separately
    for (const [vesselId, vesselArtifacts] of Object.entries(groupedArtifacts)) {
        if (vesselArtifacts.length < 2) {
            console.log(`⏭️  Skipping vessel ${vesselId}: only ${vesselArtifacts.length} artifact(s)`);
            continue;
        }

        console.log(`🚢 Processing vessel ${vesselId}: ${vesselArtifacts.length} artifacts`);

        // Call evaluation API for this vessel group
        const response = await axios.post(`${process.env.FLASK_API_URL}/evaluate/sequential`, {
            artifacts: vesselArtifacts
        });

        // Update database for this group
        const bulkOps = response.data.map(result => {
            const { _id, duplicate_index } = result;
            return {
                updateOne: {
                    filter: { _id: typeof _id === 'string' ? new ObjectId(_id) : _id },
                    update: { $set: { duplicate_index } }
                }
            };
        });

        const bulkResult = await db.qmai.collection("analysis_results").bulkWrite(bulkOps);
        console.log(`✅ Vessel ${vesselId}: Updated ${bulkResult.modifiedCount}/${response.data.length}`);

        totalUpdated += bulkResult.modifiedCount;
        totalProcessed += response.data.length;
    }

    console.log(`🎯 Total: Updated ${totalUpdated}/${totalProcessed} artifacts across all vessels`);

    // Update artifacts without duplicate_index to 0
    console.log(`🔍 Checking for artifacts without duplicate_index...`);
    const missingDuplicateIndex = await db.qmai.collection("analysis_results")
        .find({ duplicate_index: { $exists: false } })
        .toArray();

    if (missingDuplicateIndex.length > 0) {
        console.log(`📝 Found ${missingDuplicateIndex.length} artifacts without duplicate_index`);

        const updateResult = await db.qmai.collection("analysis_results")
            .updateMany(
                { duplicate_index: { $exists: false } },
                { $set: { duplicate_index: 0 } }
            );

        console.log(`✅ Set duplicate_index=0 for ${updateResult.modifiedCount} artifacts`);
    } else {
        console.log(`✅ All artifacts already have duplicate_index`);
    }
}

// Run when database is connected
db.qmai.once('open', () => main().then(() => process.exit(0)));
db.qmai.on('error', (err) => { console.error(err); process.exit(1); });
