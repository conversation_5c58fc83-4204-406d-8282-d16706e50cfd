require('dotenv').config()
const db = require('../modules/db');

const vessels = [{ "unit_id": "QMXX000003", "name": "<PERSON> GC 110-1", "thumbnail": "https://portal.quartermaster.us/GC-110-1.jpg", "region": "us-east-1", "is_live": true }, { "unit_id": "QSX0002", "name": "Unregistered", "thumbnail": null, "region": "us-east-1", "is_live": false }, { "unit_id": "QSX0003", "name": "Unregistered", "thumbnail": "https://portal.quartermaster.us/GC-110-1.jpg", "region": "us-east-1", "is_live": false }, { "unit_id": "prototype-24", "name": "BRP Capones MRRV-4404", "thumbnail": "https://portal.quartermaster.us/4404.jpg", "region": "ap-southeast-1", "is_live": true }, { "unit_id": "prototype-25", "name": "Unregistered", "thumbnail": null, "region": "ap-southeast-1", "is_live": false }, { "unit_id": "prototype-32", "name": "BRP Melchora Aquino MRRV-9702", "thumbnail": "https://portal.quartermaster.us/9702.jpg", "region": "ap-southeast-1", "is_live": true }, { "unit_id": "prototype-33", "name": "BRP Teresa Magbanua MRRV-9701", "thumbnail": "https://portal.quartermaster.us/9701.jpeg", "region": "ap-southeast-1", "is_live": false }, { "unit_id": "prototype-36", "name": "BRP Malapascua MRRV-4403", "thumbnail": "https://portal.quartermaster.us/4403.jpg", "region": "ap-southeast-1", "is_live": true }, { "unit_id": "prototype-37", "name": "BRP Cape Engaño MRRV-4411", "thumbnail": "https://portal.quartermaster.us/4411.jpeg", "region": "ap-southeast-1", "is_live": false }, { "unit_id": "QMTB000001", "name": "Unregistered", "thumbnail": null, "region": "eu-west-3", "is_live": false }, { "unit_id": "QSX0004", "name": "Unregistered", "thumbnail": null, "region": "eu-west-3", "is_live": true }, { "unit_id": "QSX0005", "name": "Unregistered", "thumbnail": null, "region": "eu-west-3", "is_live": false }, { "unit_id": "QSX0006", "name": "Unregistered", "thumbnail": null, "region": "eu-west-3", "is_live": false }]

function updateArtifactsRegion() {
    Promise.all(vessels.map(async (vessel) => {
        const collection = db.qmai.collection('analysis_results');
        if (!vessel.region) throw new Error(`No region found for vessel ${vessel.unit_id}`);
        return collection.updateMany({ unit_id: vessel.unit_id }, { $set: { aws_region: vessel.region } }).then((updates) => {
            console.log(`Updated ${vessel.unit_id} region to ${vessel.region} with ${updates.modifiedCount} updates`);
        }).catch((err) => {
            console.error(`Error updating ${vessel.unit_id} region to ${vessel.region}: ${err}`);
        });
    })).then(() => {
        console.log('All vessels updated');
    }).catch((err) => {
        console.error(`Error updating all vessels: ${err}`);
    });
}

db.qmai.on('open', () => {
    updateArtifactsRegion();
});