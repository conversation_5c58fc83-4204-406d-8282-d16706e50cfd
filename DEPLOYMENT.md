# AWS Linux Deployment Guide

Complete guide for deploying the Evaluator Microservice on AWS EC2 Linux.

## 🚀 Quick Deployment

### 1. Clone Repository
```bash
git clone <your-repo-url>
cd quartermaster-web-microservices
```

### 2. Run Main Deployment Script
```bash
chmod +x deploy.sh
./deploy.sh
```

This will:
- Install Node.js and Python 3.9
- Install PM2 for process management
- Setup both JS and Python services
- Start services automatically

## 📋 Manual Step-by-Step Deployment

### Prerequisites
```bash
# Update system
sudo yum update -y

# Install Node.js 18.x
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# Install Python 3.9
sudo yum install -y python39 python39-pip python39-devel
sudo yum groupinstall -y "Development Tools"

# Install PM2
sudo npm install -g pm2
```

### JavaScript Microservice Setup
```bash
cd js/
chmod +x deploy-js.sh
./deploy-js.sh
```

### Python API Setup
```bash
cd python/
chmod +x deploy-python.sh
./deploy-python.sh
```

## ⚙️ Process Management with PM2

### Start Services
```bash
# Start both services
pm2 start ecosystem.config.js

# Or start individually
pm2 start python/flask_evaluator_api.py --name evaluator-api --interpreter python3
pm2 start js/index.js --name js-microservice
```

### Monitor Services
```bash
# Check status
pm2 status

# View logs
pm2 logs evaluator-api
pm2 logs js-microservice

# Monitor in real-time
pm2 monit
```

### Manage Services
```bash
# Restart services
pm2 restart all
pm2 restart evaluator-api

# Stop services
pm2 stop all
pm2 stop evaluator-api

# Delete services
pm2 delete all
```

### Auto-start on Boot
```bash
# Generate startup script
pm2 startup

# Save current PM2 processes
pm2 save
```

## 🔧 Configuration

### Environment Variables

**JavaScript (.env in js/ folder):**
```env
MONGODB_URI=mongodb://localhost:27017
DB_NAME=qm
FLASK_API_URL=http://localhost:6000
NODE_ENV=production
```

**Python (environment variables):**
```bash
export PYTHONDONTWRITEBYTECODE=1
export FLASK_ENV=production
```

### Firewall Configuration
```bash
# Open ports (if needed)
sudo firewall-cmd --permanent --add-port=6000/tcp  # Python API
sudo firewall-cmd --permanent --add-port=3000/tcp  # JS service (if applicable)
sudo firewall-cmd --reload
```

## 🔍 Troubleshooting

### Check Service Status
```bash
pm2 status
pm2 logs --lines 50
```

### Python API Issues
```bash
# Check Python version
python3 --version

# Test API manually
cd python/
source venv/bin/activate
python flask_evaluator_api.py
```

### JavaScript Issues
```bash
# Check Node.js version
node --version
npm --version

# Test JS service
cd js/
npm start
```

### Common Issues

1. **Port already in use:**
   ```bash
   sudo lsof -i :6000
   sudo kill -9 <PID>
   ```

2. **Python dependencies:**
   ```bash
   cd python/
   pip3 install --user -r requirements.txt
   ```

3. **Node.js dependencies:**
   ```bash
   cd js/
   npm install
   ```

## 📊 Usage

### Run Artifact Evaluation
```bash
# Method 1: Using npm script
cd js/
npm run evaluator

# Method 2: Direct execution
cd js/
node scripts/artifact_evaluator.js
```

### API Endpoints
- **Python API**: `http://localhost:6000`
  - `POST /evaluate/sequential` - Evaluate artifacts

### Logs Location
- PM2 logs: `./logs/`
- Python API: `./logs/evaluator-api-*.log`
- JS Service: `./logs/js-microservice-*.log`

## 🔄 Updates and Redeployment

```bash
# Pull latest changes
git pull origin main

# Restart services
pm2 restart all

# Or redeploy completely
./deploy.sh
```
