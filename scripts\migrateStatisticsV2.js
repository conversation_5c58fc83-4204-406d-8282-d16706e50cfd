require("dotenv").config();
const Statistics = require("../models/Statistics");
const Vessel = require("../models/Vessel");
const { findUnitByUnitHistory } = require("../utils/functions");

function writeLine(message) {
    process.stdout.write(message + "\n");
}

const dryRun = process.argv.includes("--dry-run");

async function migrateStatisticsV2() {
    const vessels = await Vessel.find({}, { unit_id: 1, _id: 1, units_history: 1 });
    const statsDocs = await Statistics.find({});
    let totalUpdated = 0;
    for (let i = 0; i < statsDocs.length; i++) {
        const doc = statsDocs[i];
        let updated = false;
        const stats = doc.stats;
        const from = doc.fromTimestamp;
        const to = doc.toTimestamp;
        // totalVesselsDetectedbySensors
        if (stats.totalVesselsDetectedbySensors && !stats.totalVesselsDetectedbySensorsV2) {
            const v2 = {};
            Object.keys(stats.totalVesselsDetectedbySensors).forEach((unitId) => {
                const vesselResult = findUnitByUnitHistory({ vessels, from, to, unitId });
                if (vesselResult && vesselResult.vessel && vesselResult.vessel._id) {
                    v2[vesselResult.vessel._id.toString()] = stats.totalVesselsDetectedbySensors[unitId];
                }
            });
            stats.totalVesselsDetectedbySensorsV2 = v2;
            updated = true;
        }
        // totalSensorsDurationAtSea
        if (stats.totalSensorsDurationAtSea && !stats.totalSensorsDurationAtSeaV2) {
            const v2 = {};
            Object.keys(stats.totalSensorsDurationAtSea).forEach((unitId) => {
                const vesselResult = findUnitByUnitHistory({ vessels, from, to, unitId });
                if (vesselResult && vesselResult.vessel && vesselResult.vessel._id) {
                    v2[vesselResult.vessel._id.toString()] = stats.totalSensorsDurationAtSea[unitId];
                }
            });
            stats.totalSensorsDurationAtSeaV2 = v2;
            updated = true;
        }
        // totalSensorsOnlineDuration
        if (stats.totalSensorsOnlineDuration && !stats.totalSensorsOnlineDurationV2) {
            const v2 = {};
            Object.keys(stats.totalSensorsOnlineDuration).forEach((unitId) => {
                const vesselResult = findUnitByUnitHistory({ vessels, from, to, unitId });
                if (vesselResult && vesselResult.vessel && vesselResult.vessel._id) {
                    v2[vesselResult.vessel._id.toString()] = stats.totalSensorsOnlineDuration[unitId];
                }
            });
            stats.totalSensorsOnlineDurationV2 = v2;
            updated = true;
        }
        // totalSmartmastsDistanceTraveled
        if (stats.totalSmartmastsDistanceTraveled && !stats.totalSmartmastsDistanceTraveledV2) {
            const v2 = {};
            Object.keys(stats.totalSmartmastsDistanceTraveled).forEach((unitId) => {
                const vesselResult = findUnitByUnitHistory({ vessels, from, to, unitId });
                if (vesselResult && vesselResult.vessel && vesselResult.vessel._id) {
                    v2[vesselResult.vessel._id.toString()] = stats.totalSmartmastsDistanceTraveled[unitId];
                }
            });
            stats.totalSmartmastsDistanceTraveledV2 = v2;
            updated = true;
        }
        if (updated) {
            if (!dryRun) {
                await Statistics.updateOne({ _id: doc._id }, { $set: { stats: stats } });
            }
            writeLine(`Updated stats document ${doc._id}${dryRun ? " (dry run)" : ""}`);
            totalUpdated++;
        }
    }
    writeLine(`\nMigration complete. \nTotal updated: ${totalUpdated}${dryRun ? " (dry run, no changes made)" : ""}`);
    process.exit(0);
}

migrateStatisticsV2().catch((err) => {
    console.error("Migration failed:", err);
    process.exit(1);
});
