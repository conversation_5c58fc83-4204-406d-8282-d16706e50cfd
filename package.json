{"name": "gps", "version": "1.0.1", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "evaluator": "python flask_evaluator_api.py", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@mapbox/geo-viewport": "^0.5.0", "archiver": "^7.0.1", "aws-iot-device-sdk-v2": "^1.21.1", "aws-sdk": "^2.1691.0", "axios": "^1.11.0", "bson": "^6.10.3", "country-flag-icons": "^1.5.18", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "geodesy": "^2.4.0", "http": "^0.0.1-security", "json-stable-stringify": "^1.3.0", "jsonwebtoken": "^9.0.2", "mgrs": "^2.1.0", "mongoose": "^8.6.2", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "nodemon": "^3.1.6", "sharp": "^0.34.1", "socket.io": "^4.7.5", "supercluster": "^8.0.1", "winston": "^3.14.2"}, "nodemonConfig": {"ignore": ["db_backups/*"]}}